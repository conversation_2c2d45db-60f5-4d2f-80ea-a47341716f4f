/**
 * Audio Transcription Service
 * Handles converting audio files to text using OpenAI Whisper API
 */

import { config } from '../config/environment.mjs';
import axios from 'axios';
import FormData from 'form-data';

class AudioTranscriptionService {
  constructor() {
    // We'll use OpenAI Whisper API for simplicity
    // You can also implement AWS Transcribe if needed
    this.openaiApiKey = config.openai.apiKey;
  }

  /**
   * Downloads audio file from WhatsApp media URL
   * @param {string} mediaUrl - WhatsApp media URL
   * @returns {Buffer} Audio file buffer
   */
  async downloadAudioFile(mediaUrl) {
    try {
      console.log('Downloading audio file from:', mediaUrl);
      
      const response = await axios.get(mediaUrl, {
        responseType: 'arraybuffer',
        headers: {
          'Authorization': `Bearer ${config.whatsapp.accessToken}`
        }
      });

      console.log('Audio file downloaded successfully, size:', response.data.byteLength);
      return Buffer.from(response.data);

    } catch (error) {
      console.error('Error downloading audio file:', error.message);
      throw new Error('Failed to download audio file');
    }
  }

  /**
   * Transcribes audio buffer to text using OpenAI Whisper API
   * @param {Buffer} audioBuffer - Audio file buffer
   * @param {string} mediaFormat - Audio format (e.g., 'ogg', 'mp4', 'webm')
   * @returns {string} Transcribed text
   */
  async transcribeAudio(audioBuffer, mediaFormat = 'ogg') {
    try {
      console.log('Starting audio transcription with OpenAI Whisper...');

      if (!this.openaiApiKey) {
        throw new Error('OpenAI API key not configured');
      }

      // Create form data for the API request
      const formData = new FormData();
      formData.append('file', audioBuffer, {
        filename: `audio.${mediaFormat}`,
        contentType: `audio/${mediaFormat}`
      });
      formData.append('model', 'whisper-1');
      formData.append('language', 'en'); // You can make this configurable

      const response = await axios.post(
        'https://api.openai.com/v1/audio/transcriptions',
        formData,
        {
          headers: {
            'Authorization': `Bearer ${this.openaiApiKey}`,
            ...formData.getHeaders()
          },
          timeout: 30000 // 30 second timeout
        }
      );

      const transcription = response.data.text;
      console.log('Audio transcription completed:', transcription);
      return transcription;

    } catch (error) {
      console.error('Error transcribing audio:', error.response?.data || error.message);
      throw new Error('Failed to transcribe audio');
    }
  }



  /**
   * Main method to transcribe WhatsApp audio message
   * @param {string} mediaId - WhatsApp media ID
   * @param {string} mediaFormat - Audio format
   * @returns {string} Transcribed text
   */
  async transcribeWhatsAppAudio(mediaId, mediaFormat) {
    try {
      // Check if OpenAI API key is configured
      if (!this.openaiApiKey) {
        console.warn('OpenAI API key not configured, cannot transcribe audio');
        return 'I received your voice message, but audio transcription is not currently configured. Please send your message as text.';
      }

      // Get media URL from WhatsApp
      const mediaUrl = await this.getMediaUrl(mediaId);

      // Download audio file
      const audioBuffer = await this.downloadAudioFile(mediaUrl);

      // Transcribe audio
      const transcription = await this.transcribeAudio(audioBuffer, mediaFormat);

      if (!transcription || transcription.trim().length === 0) {
        throw new Error('No speech detected in audio');
      }

      return transcription;

    } catch (error) {
      console.error('Error in WhatsApp audio transcription:', error.message);

      // Return a user-friendly error message instead of throwing
      if (error.message.includes('OpenAI API key')) {
        return 'I received your voice message, but audio transcription is not currently available. Please send your message as text.';
      } else {
        return 'I had trouble understanding your voice message. Could you please try sending it again or type your message?';
      }
    }
  }

  /**
   * Gets media URL from WhatsApp media ID
   * @param {string} mediaId - WhatsApp media ID
   * @returns {string} Media URL
   */
  async getMediaUrl(mediaId) {
    try {
      const response = await axios.get(
        `https://graph.facebook.com/${config.whatsapp.apiVersion}/${mediaId}`,
        {
          headers: {
            'Authorization': `Bearer ${config.whatsapp.accessToken}`
          }
        }
      );

      return response.data.url;

    } catch (error) {
      console.error('Error getting media URL:', error.response?.data || error.message);
      throw new Error('Failed to get media URL');
    }
  }
}

export const audioTranscriptionService = new AudioTranscriptionService();
