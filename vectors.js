const { Client } = require("pg");
require("dotenv").config();

class VectorizationService {
  constructor(dbConfig) {
    this.client = new Client(dbConfig);
  }

  async connect() {
    await this.client.connect();
  }

  async disconnect() {
    await this.client.end();
  }



  formatArray(arrayData) {
    if (!arrayData || !Array.isArray(arrayData)) return "";
    return arrayData.join(", ");
  }

  cleanText(text) {
    if (!text) return "";
    return text.toString().replace(/\s+/g, " ").trim();
  }

  async vectorizeAddOns() {
    const query = `
      SELECT 
        a.add_on_id,
        a.add_on_name,
        a.add_on_type,
        a.add_on_price,
        a.fk_branch_id,
        b.branch_name,
        r.restaurant_name
      FROM "AddOns" a
      LEFT JOIN "Branches" b ON a.fk_branch_id = b.branch_id
      LEFT JOIN "Restaurants" r ON b.fk_restaurant_id = r.restaurant_id
      WHERE a.add_on_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => {
      // Format price with currency symbol
      const formattedPrice = row.add_on_price
        ? `$${parseFloat(row.add_on_price).toFixed(2)}`
        : "Price not available";

      // Create descriptive content for better AI understanding
      const content = this.cleanText(`
        Menu Add-on Information:
        
        Name: ${row.add_on_name}
        Category: ${row.add_on_type || "Standard add-on"}
        Price: ${formattedPrice}
        Available at: ${row.branch_name || "Location not specified"}
        Restaurant: ${row.restaurant_name || "Restaurant not specified"}
        
        Description: This is a menu add-on called "${
          row.add_on_name
        }" which is categorized as ${
        row.add_on_type || "a standard add-on"
      }. It costs ${formattedPrice} and is available at the ${
        row.branch_name || "specified location"
      } branch${
        row.restaurant_name ? ` of ${row.restaurant_name} restaurant` : ""
      }.
        
        Customer Context: Customers can add this item to their order for an additional charge of ${formattedPrice}. This add-on enhances the main menu items and provides additional customization options.
      `);

      return {
        id: `addon_${row.add_on_id}`,
        content: content,
        metadata: {
          type: "addon",
          entity_type: "menu_addon",
          id: row.add_on_id,
          name: row.add_on_name,
          addon_type: row.add_on_type,
          price: row.add_on_price,
          formatted_price: formattedPrice,
          branch_id: row.fk_branch_id,
          branch_name: row.branch_name,
          restaurant_name: row.restaurant_name,
          searchable_terms: [
            row.add_on_name?.toLowerCase(),
            row.add_on_type?.toLowerCase(),
            row.branch_name?.toLowerCase(),
            row.restaurant_name?.toLowerCase(),
            "addon",
            "add-on",
            "extra",
            "additional",
          ].filter(Boolean),
        },
      };
    });
  }

  async vectorizeBranches() {
    const query = `
      SELECT
        b.*,
        r.restaurant_name,
        r.phone_number as restaurant_whatsapp_number,
        r.take_orders,
        r.is_only_for_listing
      FROM "Branches" b
      LEFT JOIN "Restaurants" r ON b.fk_restaurant_id = r.restaurant_id
      WHERE b.branch_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => {
      // Format WhatsApp link with proper validation
      const whatsappNumber = row.restaurant_whatsapp_number;
      const whatsappLink = whatsappNumber
        ? `https://wa.me/${whatsappNumber.replace(/[^0-9]/g, "")}`
        : "WhatsApp contact not available";

      // Format average spend with currency
      const averageSpend = row.average_spend
        ? `AED ${parseFloat(row.average_spend).toFixed(0)}`
        : "Average spend information not available";

      // Create human-readable content
      const content = this.cleanText(`
        Restaurant Branch Information:
        
        Branch Name: ${row.branch_name}
        Restaurant: ${row.restaurant_name || "Restaurant name not specified"}
        Location: ${row.branch_address || "Address not provided"}
        Emirate: ${row.branch_emirate || "Emirate not specified"}
        
        Contact & Ordering:
        WhatsApp Ordering: ${
          whatsappNumber ? `Available - ${whatsappLink}` : "Not available"
        }
        Currently Taking Orders: ${
          row.take_orders
            ? "Yes, accepting new orders"
            : "No, temporarily closed for orders"
        }
        Order Platform: ${
          row.is_only_for_listing
            ? "External ordering only"
            : "Available through Cravin app"
        }
        
        Service Options:
        Delivery Service: ${row.branch_delivery ? "Available" : "Not available"}
        Pickup Service: ${
          row.branch_pickup
            ? "Available for pickup orders"
            : "Pickup not available"
        }
        
        Branch Details:
        Operating Hours: ${
          this.formatJSON(row.branch_timings) || "Operating hours not specified"
        }
        Average Cost per Person: ${averageSpend}
        Accepted Payment Methods: ${
          this.formatArray(row.branch_payment_modes) ||
          "Payment methods not specified"
        }
        Branch Tags: ${this.formatArray(row.branch_tags) || "No special tags"}
        ${
          row.branch_description ? `Description: ${row.branch_description}` : ""
        }
        
        Customer Information: This is ${row.branch_name} branch of ${
        row.restaurant_name || "the restaurant"
      } located in ${row.branch_emirate || "UAE"}. ${
        row.take_orders
          ? "The restaurant is currently accepting orders"
          : "The restaurant is temporarily not taking new orders"
      }. ${
        row.branch_delivery && row.branch_pickup
          ? "Both delivery and pickup services are available"
          : row.branch_delivery
          ? "Only delivery service is available"
          : row.branch_pickup
          ? "Only pickup service is available"
          : "Neither delivery nor pickup services are currently available"
      }. ${
        row.is_only_for_listing
          ? "Orders can be placed through external platforms or WhatsApp."
          : "You can order directly through the Cravin app."
      }
      `);

      return {
        id: `branch_${row.branch_id}`,
        content: content,
        metadata: {
          type: "branch",
          entity_type: "restaurant_branch",
          id: row.branch_id,
          name: row.branch_name,
          restaurant_id: row.fk_restaurant_id,
          restaurant_name: row.restaurant_name,
          address: row.branch_address,
          emirate: row.branch_emirate,
          status: row.status,

          // Service availability
          delivery_available: row.branch_delivery,
          pickup_available: row.branch_pickup,
          takes_orders: row.take_orders,

          // Contact information
          whatsapp_number: whatsappNumber,
          whatsapp_link: whatsappNumber ? whatsappLink : null,

          // Platform information
          cravin_integrated: !row.is_only_for_listing,
          external_ordering_only: row.is_only_for_listing,

          // Parsed data
          average_spend: row.average_spend,
          formatted_average_spend: averageSpend,
          payment_methods: row.branch_payment_modes,
          operating_hours: row.branch_timings,
          tags: row.branch_tags,

          // Searchable terms for better AI matching
          searchable_terms: [
            row.branch_name?.toLowerCase(),
            row.restaurant_name?.toLowerCase(),
            row.branch_emirate?.toLowerCase(),
            ...(row.branch_tags || []).map((tag) => tag?.toLowerCase()),
            "restaurant",
            "branch",
            "location",
            row.branch_delivery ? "delivery" : null,
            row.branch_pickup ? "pickup" : null,
            row.take_orders ? "open" : "closed",
          ].filter(Boolean),
        },
      };
    });
  }
  async vectorizeCategories() {
    const query = `
      SELECT 
        c.*,
        b.branch_name,
        r.restaurant_name
      FROM "Categories" c
      LEFT JOIN "Branches" b ON c.fk_branch_id = b.branch_id
      LEFT JOIN "Restaurants" r ON b.fk_restaurant_id = r.restaurant_id
      WHERE c.category_name IS NOT NULL
    `;
  
    const result = await this.client.query(query);
  
    return result.rows.map((row) => {
      // Format availability status with descriptive text
      const availabilityStatus = row.category_availability 
        ? 'Currently available for ordering'
        : 'Temporarily unavailable';
  
      // Format availability timings
      const availabilityTimings = this.formatJSON(row.category_availability_timings);
      const timingsText = availabilityTimings 
        ? `Available during: ${availabilityTimings}`
        : 'No specific timing restrictions';
  
      // Create human-readable content
      const content = this.cleanText(`
        Menu Category Information:
        
        Category Name: ${row.category_name}
        Restaurant: ${row.restaurant_name || 'Restaurant not specified'}
        Branch Location: ${row.branch_name || 'Branch not specified'}
        
        Category Details:
        ${row.category_description ? `Description: ${row.category_description}` : 'No description provided'}
        Availability Status: ${availabilityStatus}
        Service Times: ${timingsText}
        Category Status: ${row.status || 'Status not specified'}
        
        Customer Information: The "${row.category_name}" category ${row.category_availability ? 'is currently available' : 'is temporarily unavailable'} at ${row.branch_name || 'this location'}${row.restaurant_name ? ` of ${row.restaurant_name}` : ''}. ${row.category_description ? `This category includes ${row.category_description.toLowerCase()}.` : 'This category contains various food items.'} ${availabilityTimings ? `Items from this category can be ordered ${availabilityTimings.toLowerCase()}.` : 'Items from this category follow the restaurant\'s regular operating hours.'}
        
        Menu Context: This is a food category that organizes menu items into logical groups for easier browsing and ordering. Customers can explore items within this category to find their preferred dishes.
      `);
  
      return {
        id: `category_${row.category_id}`,
        content: content,
        metadata: {
          type: "category",
          entity_type: "menu_category",
          id: row.category_id,
          name: row.category_name,
          description: row.category_description,
          
          // Location information
          branch_id: row.fk_branch_id,
          branch_name: row.branch_name,
          restaurant_name: row.restaurant_name,
          
          // Availability information
          is_available: row.category_availability,
          availability_status: availabilityStatus,
          availability_timings: row.category_availability_timings,
          formatted_timings: availabilityTimings,
          
          // Status information
          status: row.status,
          active: row.status === 'active' || row.status === 'Active',
          
          // Searchable terms for better AI matching
          searchable_terms: [
            row.category_name?.toLowerCase(),
            row.restaurant_name?.toLowerCase(),
            row.branch_name?.toLowerCase(),
            ...(row.category_description ? row.category_description.toLowerCase().split(' ') : []),
            'category',
            'menu',
            'food',
            'items',
            row.category_availability ? 'available' : 'unavailable'
          ].filter(Boolean).filter(term => term.length > 2), // Filter out very short terms
          
          // Category hierarchy information
          category_level: 'main_category',
          menu_section: row.category_name
        },
      };
    });
  }
  async vectorizeClubs() {
    const query = `SELECT * FROM "Clubs" WHERE club_name IS NOT NULL`;
    const result = await this.client.query(query);
  
    return result.rows.map((row) => {
      // Format WhatsApp booking link with proper validation
      const phoneNumber = row.phone_number;
      const whatsappBookingLink = phoneNumber 
        ? `https://wa.me/${phoneNumber.replace(/[^0-9]/g, '')}` 
        : 'WhatsApp booking not available';
  
      // Format average spend with currency
      const averageSpend = row.average_spend 
        ? `AED ${parseFloat(row.average_spend).toFixed(0)}`
        : 'Average spend information not available';
  
      // Format booking status
      const bookingStatus = row.take_booking 
        ? 'Currently accepting bookings'
        : 'Bookings temporarily unavailable';
  
      // Format subscription status
      const subscriptionStatus = row.subscription_status || 'Subscription status not specified';
  
      // Create human-readable content
      const content = this.cleanText(`
        Sports Club Information:
        
        Club Name: ${row.club_name}
        Location: ${row.club_location || 'Location details not provided'}
        Emirate: ${row.club_emirate || 'Emirate not specified'}
        
        Booking Information:
        Booking Status: ${bookingStatus}
        WhatsApp Booking: ${phoneNumber ? `Available - ${whatsappBookingLink}` : 'Not available'}
        Booking Policy: ${this.formatArray(row.booking_policy) || 'Standard booking terms apply'}
        
        Facility Details:
        Operating Hours: ${this.formatJSON(row.club_timings) || 'Operating hours not specified'}
        Average Cost per Visit: ${averageSpend}
        Accepted Payment Methods: ${this.formatJSON(row.payment_methods) || 'Payment methods not specified'}
        Club Features: ${this.formatArray(row.club_tags) || 'No special features listed'}
        Subscription Status: ${subscriptionStatus}
        
        Club Description: ${row.club_description || 'Description not provided'}
        
        Customer Information: ${row.club_name} is a sports club located in ${row.club_emirate || 'UAE'}${row.club_location ? ` at ${row.club_location}` : ''}. ${row.take_booking ? 'The club is currently accepting bookings and reservations' : 'The club is temporarily not accepting new bookings'}. ${phoneNumber ? `You can make bookings through WhatsApp by contacting ${phoneNumber}.` : 'Direct booking contact is not available at the moment.'} The typical cost per visit is around ${averageSpend}. ${row.booking_policy ? 'Please review the booking policy before making a reservation.' : ''}
        
        Sports & Recreation Context: This is a sports and recreation facility that offers various activities and amenities. Customers can book facilities, participate in sports activities, and enjoy recreational services.
      `);
  
      return {
        id: `club_${row.club_id}`,
        content: content,
        metadata: {
          type: "club",
          entity_type: "sports_club",
          id: row.club_id,
          name: row.club_name,
          description: row.club_description,
          
          // Location information
          location: row.club_location,
          emirate: row.club_emirate,
          full_address: row.club_location && row.club_emirate 
            ? `${row.club_location}, ${row.club_emirate}` 
            : row.club_location || row.club_emirate,
          
          // Contact and booking information
          phone_number: phoneNumber,
          whatsapp_booking_link: phoneNumber ? whatsappBookingLink : null,
          takes_bookings: row.take_booking,
          booking_status: bookingStatus,
          booking_policy: row.booking_policy,
          
          // Financial information
          average_spend: row.average_spend,
          formatted_average_spend: averageSpend,
          payment_methods: row.payment_methods,
          
          // Operational information
          operating_hours: row.club_timings,
          subscription_status: row.subscription_status,
          club_features: row.club_tags,
          
          // Status information
          active: row.take_booking && row.subscription_status !== 'inactive',
          
          // Searchable terms for better AI matching
          searchable_terms: [
            row.club_name?.toLowerCase(),
            row.club_location?.toLowerCase(),
            row.club_emirate?.toLowerCase(),
            ...(row.club_tags || []).map(tag => tag?.toLowerCase()),
            ...(row.club_description ? row.club_description.toLowerCase().split(' ') : []),
            'sports',
            'club',
            'facility',
            'recreation',
            'booking',
            row.take_booking ? 'available' : 'unavailable',
            'activities'
          ].filter(Boolean).filter(term => term.length > 2),
          
          // Business context
          business_type: 'sports_recreation',
          service_type: 'facility_booking'
        },
      };
    });
  }

  async vectorizeCourts() {
    const query = `
      SELECT 
        c.*,
        f.facility_name,
        f.facility_category,
        f.facility_type,
        cl.club_name
      FROM "Courts" c
      LEFT JOIN "Facilities" f ON c.fk_facility_id = f.facility_id
      LEFT JOIN "Clubs" cl ON f.fk_club_id = cl.club_id
      WHERE c.court_name IS NOT NULL
    `;
  
    const result = await this.client.query(query);
  
    return result.rows.map((row) => {
      // Format status with descriptive text
      const courtStatus = row.status 
        ? 'Available for booking'
        : 'Currently unavailable';
  
      // Determine court type and sport
      const courtType = row.facility_type || 'Standard court';
      const sportCategory = row.facility_category || 'General sports';
  
      // Create human-readable content
      const content = this.cleanText(`
        Sports Court Information:
        
        Court Name: ${row.court_name}
        Sport/Activity: ${sportCategory}
        Court Type: ${courtType}
        Located at: ${row.club_name || 'Club name not specified'}
        Facility: ${row.facility_name || 'Part of general sports facility'}
        
        Availability Status: ${courtStatus}
        Court Status: ${row.status ? 'Active and operational' : 'Inactive or under maintenance'}
        
        Court Details: This is ${row.court_name}, a ${courtType.toLowerCase()} designed for ${sportCategory.toLowerCase()} activities. ${row.club_name ? `The court is located at ${row.club_name}` : 'The court is part of a sports facility'}${row.facility_name ? ` within the ${row.facility_name}` : ''}. ${row.status ? 'The court is currently active and available for bookings.' : 'The court is currently inactive and not available for reservations.'}
        
        Booking Information: ${row.status ? `This ${sportCategory.toLowerCase()} court can be reserved for sports activities, training sessions, or recreational play. Contact the facility to check availability and make bookings.` : 'This court is temporarily unavailable for bookings. Please contact the facility for updates on when it will be operational again.'}
        
        Sports Context: This is a dedicated sports court that provides a designated space for ${sportCategory.toLowerCase()} activities. Courts are essential facilities for organized sports, training, and recreational activities.
      `);
  
      return {
        id: `court_${row.court_id}`,
        content: content,
        metadata: {
          type: "court",
          entity_type: "sports_court",
          id: row.court_id,
          name: row.court_name,
          
          // Facility and location information
          facility_id: row.fk_facility_id,
          facility_name: row.facility_name,
          club_name: row.club_name,
          
          // Court specifications
          court_type: row.facility_type,
          sport_category: row.facility_category,
          activity_type: sportCategory,
          
          // Status and availability
          is_active: row.status,
          status: row.status,
          availability_status: courtStatus,
          bookable: row.status,
          
          // Hierarchy information
          parent_facility: row.facility_name,
          parent_club: row.club_name,
          
          // Searchable terms for better AI matching
          searchable_terms: [
            row.court_name?.toLowerCase(),
            row.facility_name?.toLowerCase(),
            row.club_name?.toLowerCase(),
            row.facility_category?.toLowerCase(),
            row.facility_type?.toLowerCase(),
            'court',
            'sports',
            'facility',
            'booking',
            'reservation',
            row.status ? 'available' : 'unavailable',
            row.status ? 'active' : 'inactive'
          ].filter(Boolean),
          
          // Business context
          business_type: 'sports_facility',
          service_type: 'court_booking',
          facility_level: 'court'
        },
      };
    });
  }

  async vectorizeDeliveryZones() {
    const query = `
      SELECT 
        dz.*,
        b.branch_name,
        r.restaurant_name,
        s.shop_name
      FROM "DeliveryZones" dz
      LEFT JOIN "Branches" b ON dz.fk_branch_id = b.branch_id
      LEFT JOIN "Restaurants" r ON dz.fk_restaurant_id = r.restaurant_id
      LEFT JOIN "Shops" s ON dz.fk_shop_id = s.shop_id
      WHERE dz.zone_name IS NOT NULL
    `;
  
    const result = await this.client.query(query);
  
    return result.rows.map((row) => {
      // Format delivery fee with currency
      const deliveryFee = row.delivery_fee 
        ? `AED ${parseFloat(row.delivery_fee).toFixed(2)}`
        : 'Delivery fee not specified';
  
      // Format minimum cart amount with currency
      const minCartAmount = row.min_cart_amount 
        ? `AED ${parseFloat(row.min_cart_amount).toFixed(2)}`
        : 'No minimum order requirement';
  
      // Determine the business type and name
      const businessInfo = {
        type: row.restaurant_name ? 'restaurant' : row.shop_name ? 'shop' : 'business',
        name: row.restaurant_name || row.shop_name || 'Business not specified',
        location: row.branch_name || 'Location not specified'
      };
  
      // Format delivery zone status
      const zoneStatus = row.status 
        ? 'Active delivery zone'
        : 'Inactive delivery zone';
  
      // Create human-readable content
      const content = this.cleanText(`
        Delivery Zone Information:
        
        Zone Name: ${row.zone_name}
        Service Provider: ${businessInfo.name}
        Business Type: ${businessInfo.type === 'restaurant' ? 'Restaurant' : businessInfo.type === 'shop' ? 'Shop' : 'Business'}
        ${businessInfo.type === 'restaurant' ? 'Branch Location' : 'Shop Location'}: ${businessInfo.location}
        
        Delivery Details:
        Delivery Fee: ${deliveryFee}
        Minimum Order Amount: ${minCartAmount}
        Zone Status: ${zoneStatus}
        Service Area Coordinates: ${this.formatJSON(row.coordinates) || 'Geographic boundaries not specified'}
        
        Delivery Information: The "${row.zone_name}" delivery zone is served by ${businessInfo.name}${businessInfo.location !== 'Location not specified' ? ` from their ${businessInfo.location} location` : ''}. ${row.status ? 'This zone is currently active and accepting delivery orders' : 'This zone is temporarily inactive and not accepting deliveries'}. ${row.delivery_fee ? `Delivery to this area costs ${deliveryFee}` : 'Delivery fee information is not available'}. ${row.min_cart_amount ? `You need to order at least ${minCartAmount} worth of items for delivery to this zone` : 'There is no minimum order requirement for this delivery area'}.
        
        Geographic Coverage: This delivery zone covers the ${row.zone_name} area${row.coordinates ? ' with specific geographic boundaries defined by coordinates' : ''}. Customers within this zone can place orders for delivery service${row.status ? '' : ', but the zone is currently inactive'}.
        
        Customer Context: Delivery zones help determine if your location is eligible for delivery service, what fees apply, and any minimum order requirements. Make sure your delivery address falls within this zone's coverage area.
      `);
  
      return {
        id: `delivery_zone_${row.zone_id}`,
        content: content,
        metadata: {
          type: "delivery_zone",
          entity_type: "service_delivery_zone",
          id: row.zone_id,
          name: row.zone_name,
          
          // Business association
          branch_id: row.fk_branch_id,
          restaurant_id: row.fk_restaurant_id,
          shop_id: row.fk_shop_id,
          business_name: businessInfo.name,
          business_type: businessInfo.type,
          location_name: businessInfo.location,
          
          // Delivery terms
          delivery_fee: row.delivery_fee,
          formatted_delivery_fee: deliveryFee,
          min_cart_amount: row.min_cart_amount,
          formatted_min_cart: minCartAmount,
          has_minimum_order: !!row.min_cart_amount,
          free_delivery: !row.delivery_fee || row.delivery_fee === 0,
          
          // Geographic information
          coordinates: row.coordinates,
          has_coordinates: !!row.coordinates,
          geographic_boundaries: row.coordinates,
          
          // Status information
          is_active: row.status,
          zone_status: zoneStatus,
          accepts_deliveries: row.status,
          
          // Service information
          delivery_available: row.status,
          service_type: 'delivery',
          coverage_area: row.zone_name,
          
          // Searchable terms for better AI matching
          searchable_terms: [
            row.zone_name?.toLowerCase(),
            row.branch_name?.toLowerCase(),
            row.restaurant_name?.toLowerCase(),
            row.shop_name?.toLowerCase(),
            'delivery',
            'zone',
            'area',
            'coverage',
            'service',
            businessInfo.type,
            row.status ? 'active' : 'inactive',
            row.status ? 'available' : 'unavailable'
          ].filter(Boolean),
          
          // Business context
          service_category: 'delivery_logistics',
          coverage_type: 'geographic_zone'
        },
      };
    });
  }

  async vectorizeDiscounts() {
    const query = `
      SELECT 
        d.*,
        c.club_name
      FROM "Discounts" d
      LEFT JOIN "Clubs" c ON d.fk_club_id = c.club_id
      WHERE d.discount_name IS NOT NULL
    `;
  
    const result = await this.client.query(query);
  
    return result.rows.map((row) => {
      // Format discount value with proper type indication
      const formatDiscountValue = () => {
        if (!row.discount_value) return 'Discount value not specified';
        
        if (row.discount_type?.toLowerCase() === 'percentage') {
          return `${row.discount_value}% off`;
        } else if (row.discount_type?.toLowerCase() === 'fixed' || row.discount_type?.toLowerCase() === 'amount') {
          return `AED ${parseFloat(row.discount_value).toFixed(0)} off`;
        }
        return `${row.discount_value} discount`;
      };
  
      // Format usage limits
      const usageInfo = () => {
        const perCustomer = row.uses_per_customer;
        const totalUses = row.total_uses;
        
        let usage = [];
        if (perCustomer) usage.push(`${perCustomer} use${perCustomer > 1 ? 's' : ''} per customer`);
        if (totalUses) usage.push(`${totalUses} total uses available`);
        
        return usage.length > 0 ? usage.join(', ') : 'No usage limits specified';
      };
  
      // Format amount requirements
      const formatAmountRequirements = () => {
        const minAmount = row.min_amount ? `AED ${parseFloat(row.min_amount).toFixed(0)}` : null;
        const maxAmount = row.max_amount ? `AED ${parseFloat(row.max_amount).toFixed(0)}` : null;
        
        if (minAmount && maxAmount) {
          return `Valid on orders between ${minAmount} - ${maxAmount}`;
        } else if (minAmount) {
          return `Minimum order amount: ${minAmount}`;
        } else if (maxAmount) {
          return `Maximum discount applicable on orders up to ${maxAmount}`;
        }
        return 'No minimum order requirements';
      };
  
      // Format discount status
      const discountStatus = row.status 
        ? 'Currently active and available'
        : 'Inactive or expired';
  
      // Create human-readable content
      const content = this.cleanText(`
        Discount & Promotion Information:
        
        Promotion Name: ${row.discount_name}
        Discount Code: ${row.discount_code || 'No code required'}
        Applicable at: ${row.club_name || 'All participating locations'}
        
        Discount Details:
        Discount Type: ${row.discount_type || 'Standard discount'}
        Discount Value: ${formatDiscountValue()}
        Order Requirements: ${formatAmountRequirements()}
        
        Usage Information:
        Usage Limits: ${usageInfo()}
        Current Status: ${discountStatus}
        
        Promotion Description: ${row.discount_description || 'Standard promotional offer'}
        
        Customer Information: The "${row.discount_name}" promotion offers ${formatDiscountValue().toLowerCase()}${row.club_name ? ` at ${row.club_name}` : ' at participating locations'}. ${row.discount_code ? `Use code "${row.discount_code}" when booking or making a purchase.` : 'No promotional code required - discount will be automatically applied.'} ${row.min_amount ? `This offer requires a minimum spend of AED ${parseFloat(row.min_amount).toFixed(0)}.` : ''} ${row.status ? 'This promotion is currently active and can be used.' : 'This promotion is no longer available or has expired.'}
        
        Savings Context: This is a promotional discount designed to provide value to customers. ${row.discount_type?.toLowerCase() === 'percentage' ? 'Percentage discounts provide proportional savings based on your total order.' : 'Fixed amount discounts provide a set reduction in your total cost.'} Check the usage limits and order requirements to ensure eligibility.
      `);
  
      return {
        id: `discount_${row.discount_id}`,
        content: content,
        metadata: {
          type: "discount",
          entity_type: "promotional_discount",
          id: row.discount_id,
          name: row.discount_name,
          description: row.discount_description,
          
          // Discount details
          discount_code: row.discount_code,
          discount_type: row.discount_type,
          discount_value: row.discount_value,
          formatted_discount_value: formatDiscountValue(),
          
          // Location information
          club_id: row.fk_club_id,
          club_name: row.club_name,
          applicable_location: row.club_name || 'multiple_locations',
          
          // Amount requirements
          min_amount: row.min_amount,
          max_amount: row.max_amount,
          formatted_min_amount: row.min_amount ? `AED ${parseFloat(row.min_amount).toFixed(0)}` : null,
          formatted_max_amount: row.max_amount ? `AED ${parseFloat(row.max_amount).toFixed(0)}` : null,
          
          // Usage limits
          uses_per_customer: row.uses_per_customer,
          total_uses: row.total_uses,
          usage_info: usageInfo(),
          
          // Status information
          is_active: row.status,
          status: row.status,
          availability_status: discountStatus,
          
          // Discount categorization
          requires_code: !!row.discount_code,
          has_minimum_spend: !!row.min_amount,
          has_usage_limits: !!(row.uses_per_customer || row.total_uses),
          
          // Searchable terms for better AI matching
          searchable_terms: [
            row.discount_name?.toLowerCase(),
            row.discount_code?.toLowerCase(),
            row.club_name?.toLowerCase(),
            row.discount_type?.toLowerCase(),
            'discount',
            'promotion',
            'offer',
            'savings',
            'deal',
            row.status ? 'active' : 'expired',
            row.discount_code ? 'code' : 'automatic'
          ].filter(Boolean),
          
          // Business context
          business_type: 'promotional_offer',
          service_type: 'discount_promotion',
          promotion_category: row.discount_type || 'general'
        },
      };
    });
  }

  async vectorizeFAQCategories() {
    const query = `SELECT * FROM "FAQCategory" WHERE category_name IS NOT NULL`;
    const result = await this.client.query(query);
  
    return result.rows.map((row) => {
      // Format product information
      const productInfo = row.product || 'General inquiries';
      
      // Create human-readable content
      const content = this.cleanText(`
        FAQ Category Information:
        
        Category Name: ${row.category_name}
        Product/Service: ${productInfo}
        ${row.description ? `Category Description: ${row.description}` : ''}
        
        Help Section: This FAQ category covers questions and answers related to ${row.category_name.toLowerCase()} for ${productInfo.toLowerCase()}. ${row.description ? `Specifically, this section addresses ${row.description.toLowerCase()}.` : 'This section contains frequently asked questions that customers commonly have about this topic.'}
        
        Customer Support Context: This is a help category that organizes frequently asked questions to provide quick answers to common customer inquiries. Users can browse this category to find answers to questions about ${row.category_name.toLowerCase()} without needing to contact support directly.
        
        Usage Guide: Customers looking for help with ${row.category_name.toLowerCase()} can find relevant answers in this FAQ section. This category is part of the self-service support system designed to provide instant answers to common questions about ${productInfo.toLowerCase()}.
      `);
  
      return {
        id: `faq_category_${row.categroy_id}`, // Note: keeping the original typo in case it's used elsewhere
        content: content,
        metadata: {
          type: "faq_category",
          entity_type: "help_category",
          id: row.categroy_id, // Note: keeping the original typo
          name: row.category_name,
          description: row.description,
          product: row.product,
          product_service: productInfo,
          
          // Support classification
          support_category: row.category_name,
          help_section: 'frequently_asked_questions',
          self_service: true,
          
          // Searchable terms for better AI matching
          searchable_terms: [
            row.category_name?.toLowerCase(),
            row.product?.toLowerCase(),
            ...(row.description ? row.description.toLowerCase().split(' ') : []),
            'faq',
            'help',
            'support',
            'questions',
            'answers',
            'frequently',
            'asked',
            'assistance',
            'guide'
          ].filter(Boolean).filter(term => term.length > 2),
          
          // Context information
          purpose: 'customer_self_service',
          content_type: 'faq_category',
          support_level: 'self_service'
        },
      };
    });
  }

  async vectorizeFAQQuestions() {
    const query = `
      SELECT 
        fq.*,
        fc.category_name,
        c.club_name,
        r.restaurant_name,
        s.shop_name,
        b.branch_name
      FROM "FAQQuestions" fq
      LEFT JOIN "FAQCategory" fc ON fq.fk_category_id = fc.categroy_id
      LEFT JOIN "Clubs" c ON fq.fk_club_id = c.club_id
      LEFT JOIN "Restaurants" r ON fq.fk_restaurant_id = r.restaurant_id
      LEFT JOIN "Shops" s ON fq.fk_shop_id = s.shop_id
      LEFT JOIN "Branches" b ON fq.fk_branch_id = b.branch_id
      WHERE fq.question IS NOT NULL
    `;
  
    const result = await this.client.query(query);
  
    return result.rows.map((row) => {
      // Determine the business entity and context
      const businessEntity = row.club_name || row.restaurant_name || row.shop_name || 'General';
      const businessType = row.club_name ? 'Sports Club' : 
                          row.restaurant_name ? 'Restaurant' : 
                          row.shop_name ? 'Shop' : 'General Service';
      
      // Format location context
      const locationContext = row.branch_name ? ` at ${row.branch_name} branch` : '';
      
      // Create human-readable content
      const content = this.cleanText(`
        Frequently Asked Question:
        
        Question: ${row.question}
        Answer: ${row.answer || 'Answer not provided yet'}
        
        FAQ Context:
        Category: ${row.category_name || 'General inquiries'}
        Business: ${businessEntity}${locationContext}
        Business Type: ${businessType}
        
        Customer Support Information: This is a commonly asked question about ${businessEntity}${locationContext}. ${row.answer ? 'The complete answer is provided above to help customers understand this topic.' : 'This question is being addressed and a detailed answer will be available soon.'} This FAQ falls under the "${row.category_name || 'general inquiries'}" category.
        
        Support Context: This FAQ helps customers get quick answers to common questions about ${businessType.toLowerCase()} services${locationContext}. It's designed to provide immediate assistance and reduce the need for direct customer support contact.
        
        Question Keywords: The question addresses topics related to ${row.category_name?.toLowerCase() || 'general services'}, ${businessType.toLowerCase()}, and customer inquiries about ${businessEntity}.
      `);
  
      return {
        id: `faq_${row.question_id}`,
        content: content,
        metadata: {
          type: "faq",
          entity_type: "customer_support",
          id: row.question_id,
          
          // Question and answer content
          question: row.question,
          answer: row.answer,
          has_answer: !!row.answer,
          
          // Categorization
          category_name: row.category_name,
          faq_category: row.category_name || 'general',
          
          // Business entity information
          business_entity: businessEntity,
          business_type: businessType.toLowerCase().replace(' ', '_'),
          club_id: row.fk_club_id,
          club_name: row.club_name,
          restaurant_id: row.fk_restaurant_id,
          restaurant_name: row.restaurant_name,
          shop_id: row.fk_shop_id,
          shop_name: row.shop_name,
          branch_id: row.fk_branch_id,
          branch_name: row.branch_name,
          
          // Location context
          location_context: locationContext,
          has_location_specific: !!row.branch_name,
          
          // Support classification
          support_level: 'faq',
          customer_facing: true,
          
          // Searchable terms for better AI matching
          searchable_terms: [
            // Question analysis
            ...(row.question ? row.question.toLowerCase().split(/\s+/) : []),
            // Answer analysis
            ...(row.answer ? row.answer.toLowerCase().split(/\s+/) : []),
            // Entity names
            row.club_name?.toLowerCase(),
            row.restaurant_name?.toLowerCase(),
            row.shop_name?.toLowerCase(),
            row.branch_name?.toLowerCase(),
            row.category_name?.toLowerCase(),
            // General terms
            'faq',
            'question',
            'help',
            'support',
            'customer',
            'inquiry',
            businessType.toLowerCase().replace(' ', '_')
          ].filter(Boolean)
           .filter(term => term.length > 2)
           .filter(term => !['the', 'and', 'for', 'are', 'you', 'can', 'will', 'how', 'what', 'when', 'where', 'why'].includes(term)),
          
          // Question classification
          question_type: this.classifyFAQQuestion(row.question),
          priority: row.category_name?.toLowerCase().includes('urgent') || 
                   row.question?.toLowerCase().includes('urgent') ? 'high' : 'normal'
        },
      };
    });
  }
  
  // Helper method to classify FAQ questions
  async classifyFAQQuestion(question) {
    if (!question) return 'general';
    
    const lowerQuestion = question.toLowerCase();
    
    if (lowerQuestion.includes('book') || lowerQuestion.includes('reserv')) return 'booking';
    if (lowerQuestion.includes('pay') || lowerQuestion.includes('price') || lowerQuestion.includes('cost')) return 'payment';
    if (lowerQuestion.includes('cancel') || lowerQuestion.includes('refund')) return 'cancellation';
    if (lowerQuestion.includes('hour') || lowerQuestion.includes('time') || lowerQuestion.includes('open')) return 'hours';
    if (lowerQuestion.includes('location') || lowerQuestion.includes('address') || lowerQuestion.includes('where')) return 'location';
    if (lowerQuestion.includes('contact') || lowerQuestion.includes('phone') || lowerQuestion.includes('email')) return 'contact';
    if (lowerQuestion.includes('delivery') || lowerQuestion.includes('pickup')) return 'service';
    if (lowerQuestion.includes('menu') || lowerQuestion.includes('food') || lowerQuestion.includes('item')) return 'menu';
    
    return 'general';
  }


/**
 * Utility  async to format arrays into a human-readable string.
 * Replace this with your actual implementation.
 */
 async formatArray(arr) {
  if (!arr || arr.length === 0) return "None specified";
  return arr.join(", ");
}

/**
 * Utility  async to format JSON objects into a human-readable string.
 * This is a crucial helper and needs to be robust for your specific JSON structures.
 * Replace this with your actual, more sophisticated implementation.
 */
 async formatJSON(json) {
  if (!json) return "None specified";
  try {
    const obj = typeof json === 'string' ? JSON.parse(json) : json;
    if (Object.keys(obj).length === 0) return "None specified";

    // Example for formatting timings (assuming keys like 'monday', 'start_time', 'end_time')
    if (obj.monday || obj.start_time || obj.end_time) {
      let formatted = [];
      const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
      for (const day of days) {
        if (Array.isArray(obj[day])) {
          const dailyTimings = obj[day].map(t => `${t.start_time} to ${t.end_time}`).join(", ");
          if (dailyTimings) formatted.push(`${day.charAt(0).toUpperCase() + day.slice(1)}: ${dailyTimings}`);
        } else if (obj[day] && obj[day].start_time && obj[day].end_time) {
          formatted.push(`${day.charAt(0).toUpperCase() + day.slice(1)}: ${obj[day].start_time} to ${obj[day].end_time}`);
        }
      }
      if (formatted.length > 0) return formatted.join("; ");
    }

    // Example for formatting simple key-value pairs
    return Object.entries(obj)
      .map(([key, value]) => {
        // Simple heuristic to make keys more readable (e.g., "club_name" -> "Club Name")
        const readableKey = key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
        return `${readableKey}: ${value}`;
      })
      .join("; ");

  } catch (e) {
    console.error("Error formatting JSON:", e, json);
    return "Not available or invalid format";
  }
}


  async  vectorizeFacilities(client) {
    const query = `
      SELECT
        f.*,
        c.club_name
      FROM "Facilities" f
      LEFT JOIN "Clubs" c ON f.fk_club_id = c.club_id
      WHERE f.facility_name IS NOT NULL
    `;

    const result = await client.query(query);

    return result.rows.map((row) => ({
      id: `facility_${row.facility_id}`,
      content: this.cleanText(`
        This is a facility named **${row.facility_name}**, located at the **${row.club_name || "an unspecified club"}**.
        It falls under the **${row.facility_category}** category and is a **${row.facility_type}** type facility.
        Each time slot available for booking is **${row.time_slot_duration} minutes long**.
        Available court types include: **${this.formatArray(row.court_types)}**.
        For pricing details, the options are: **${this.formatJSON(row.pricing)}**.
        The facility operates during the following hours: **${this.formatJSON(row.facility_timing)}**.
        The primary pricing option is **${row.pricing_option || "not specified"}**.
        A minimum duration of **${row.facility_min_duration} minutes** is required for bookings.
        Currently, this facility is **${row.status ? "active" : "inactive"}**.
      `),
      metadata: {
        type: "facility",
        id: row.facility_id,
        name: row.facility_name,
        club_id: row.fk_club_id,
        club_name: row.club_name,
        category: row.facility_category,
        facility_type: row.facility_type,
        status: row.status,
      },
    }));
  }

  async  vectorizeFoodDiscounts(client) {
    const query = `
      SELECT
        fd.*,
        r.restaurant_name
      FROM "FoodDiscounts" fd
      LEFT JOIN "Restaurants" r ON fd.fk_restaurant_id = r.restaurant_id
      WHERE fd.discount_name IS NOT NULL
    `;

    const result = await client.query(query);

    return result.rows.map((row) => ({
      id: `food_discount_${row.discount_id}`,
      content: this.cleanText(`
        Here's a food discount called **${row.discount_name}**.
        Its code is **${row.discount_code || "not available"}**.
        This discount is offered by **${row.restaurant_name || "an unknown restaurant"}**.
        It's a **${row.discount_type}** type of discount with a value of **${row.discount_value}**.
        To be eligible, a minimum amount of **${row.min_amount || "N/A"}** might be required, with a maximum discount of **${row.max_amount || "N/A"}**.
        Each customer can use this discount **${row.uses_per_customer} time(s)**.
        There are **${row.total_uses || "no specified total"}** uses available in total.
        The discount can be used via **${row.usage_method || "an unspecified method"}**.
        Currently, this discount is **${row.status ? "active" : "inactive"}**.
      `),
      metadata: {
        type: "food_discount",
        id: row.discount_id,
        name: row.discount_name,
        code: row.discount_code,
        restaurant_id: row.fk_restaurant_id,
        restaurant_name: row.restaurant_name,
        discount_type: row.discount_type,
        status: row.status,
      },
    }));
  }

  async  vectorizeFoodItemTags(client) {
    const query = `SELECT * FROM "FoodItemTags" WHERE tag_name IS NOT NULL`;
    const result = await client.query(query);

    return result.rows.map((row) => ({
      id: `food_tag_${row.tag_id}`,
      content: this.cleanText(`
        This is a food item tag named **${row.tag_name}**.
        Its reference information is **${row.tag_reference || "not provided"}**.
      `),
      metadata: {
        type: "food_tag",
        id: row.tag_id,
        name: row.tag_name,
        reference: row.tag_reference,
      },
    }));
  }

  async  vectorizeFoodLandingPages(client) {
    const query = `
      SELECT
        flp.*,
        r.restaurant_name,
        b.branch_name,
        r.is_only_for_listing
      FROM "FoodLandingPage" flp
      LEFT JOIN "Restaurants" r ON flp.fk_restaurant_id = r.restaurant_id
      LEFT JOIN "Branches" b ON flp.branch_id = b.branch_id
    `;

    const result = await client.query(query);

    return result.rows.map((row) => ({
      id: `food_landing_${row.fk_restaurant_id}_${row.branch_id}`,
      content: this.cleanText(`
        This landing page features the restaurant **${row.restaurant_name || "an unnamed restaurant"}**, specifically its **${row.branch_name || "main"}** branch.
        It is located in the **${row.branch_emirate || "an unspecified emirate"}**.
        Customers typically spend an average of **${row.average_spend || "an unlisted amount"}**.
        This location is associated with tags like: **${this.formatArray(row.branch_tags)}**.
        Some of its popular dishes include: **${this.formatArray(row.popular_dishes)}**.
        It is particularly known for: **${row.known_for || "no specific highlights"}**.
        You can find more information here: **${this.formatArray(row.more_info)}**.
        For direct contact, the landline numbers are: **${this.formatArray(row.restaurant_landline_numbers)}**.
        Their social media presence can be found through these links: **${this.formatJSON(row.social_links)}**.
        Please note: **${row.is_only_for_listing ? "You cannot order directly from Cravin; this is for listing purposes only." : "You can order directly from Cravin."}**
      `),
      metadata: {
        type: "food_landing",
        restaurant_id: row.fk_restaurant_id,
        branch_id: row.branch_id,
        restaurant_name: row.restaurant_name,
        branch_name: row.branch_name,
        emirate: row.branch_emirate,
        show_in_landing: row.show_in_landing_page,
      },
    }));
  }

  async  vectorizeItems(client) {
    const query = `
      SELECT
        i.*,
        c.category_name,
        b.branch_name,
        r.restaurant_name,
        t.tag_name
      FROM "Items" i
      LEFT JOIN "Categories" c ON i.fk_category_id = c.category_id
      LEFT JOIN "Branches" b ON i.fk_branch_id = b.branch_id
      LEFT JOIN "Restaurants" r ON b.fk_restaurant_id = r.restaurant_id
      LEFT JOIN "FoodItemTags" t ON i.fk_tag_id = t.tag_id
      WHERE i.item_name IS NOT NULL
    `;

    const result = await client.query(query);

    return result.rows.map((row) => ({
      id: `item_${row.item_id}`,
      content: this.cleanText(`
        This food item is **${row.item_name}**.
        Its description is: "${row.item_description || "No description provided"}".
        It belongs to the **${row.category_name || "unspecified"}** category.
        You can find it at the **${row.branch_name || "main"}** branch of **${row.restaurant_name || "an unknown restaurant"}**.
        It's classified as a **${row.item_type}** item and costs **${row.item_price}**.
        The item is currently **${row.item_status}**.
        It's tagged as **${row.tag_name || "untagged"}**.
        This item is **${row.is_recommended ? "recommended" : "not recommended"}**.
        It is **${row.is_spicy ? "spicy" : "not spicy"}**.
        Available variants include: **${this.formatJSON(row.item_variants)}**.
        You can also choose from these add-ons: **${this.formatJSON(row.item_add_ons_group)}**.
        Customers have given it an average rating of **${
          row.rating_sum && row.rating_count
            ? (row.rating_sum / row.rating_count).toFixed(1) + " out of 5"
            : "N/A"
        }**.
      `),
      metadata: {
        type: "item",
        id: row.item_id,
        name: row.item_name,
        category: row.category_name,
        branch_id: row.fk_branch_id,
        branch_name: row.branch_name,
        restaurant_name: row.restaurant_name,
        item_type: row.item_type,
        price: row.item_price,
        status: row.item_status,
        is_recommended: row.is_recommended,
        is_spicy: row.is_spicy,
      },
    }));
  }

  async  vectorizeRestaurants(client) {
    const query = `SELECT * FROM "Restaurants" WHERE restaurant_name IS NOT NULL`;
    const result = await client.query(query);

    return result.rows.map((row) => ({
      id: `restaurant_${row.restaurant_id}`,
      content: this.cleanText(`
        This is **${row.restaurant_name}**.
        They **${row.take_orders ? "do take orders" : "do not take orders"}**.
        Their menu type is **${row.restaurant_menu_type || "not specified"}**.
        For ordering, please refer to their policy: **${this.formatArray(row.order_policy)}**.
        They accept the following payment methods: **${this.formatJSON(row.payment_methods)}**.
        Their operating hours are: **${this.formatJSON(row.restaurant_timings)}**.
        You can find them on social media at: **${this.formatJSON(row.restaurant_social_links)}**.
        Their subscription status is **${row.subscription_status ? "active" : "inactive"}**.
        Orders are **${row.restaurant_auto_accept ? "automatically accepted" : "not automatically accepted"}**.
        Customer support is **${row.enable_support ? "enabled" : "not enabled"}**.
        A pickup module is **${row.pickup_module ? "available" : "not available"}**.
        You can also order directly through WhatsApp by clicking this link: **https://wa.me/${row.phone_number}**.
        Regarding ordering directly from Cravin: **${row.is_only_for_listing ? "This restaurant is only for listing and direct orders are not supported through Cravin." : "You can order your favorite food directly through Cravin."}**
      `),
      metadata: {
        type: "restaurant",
        id: row.restaurant_id,
        name: row.restaurant_name,
        phone: row.phone_number,
        takes_orders: row.take_orders,
        menu_type: row.restaurant_menu_type,
        subscription_status: row.subscription_status,
        pickup_available: row.pickup_module,
        order_link_through_whatsapp: "https://wa.me/" + row.phone_number,
        restaurant_under_cravin: !row.is_only_for_listing,
      },
    }));
  }

  async  vectorizeShops(client) {
    const query = `SELECT * FROM "Shops" WHERE shop_name IS NOT NULL`;
    const result = await client.query(query);

    return result.rows.map((row) => ({
      id: `shop_${row.shop_id}`,
      content: this.cleanText(`
        This is the shop named **${row.shop_name}**.
        You can reach them at **${row.phone_number}**.
        They **${row.take_orders ? "do take orders" : "do not take orders"}**.
        Please review their order policy: **${this.formatArray(row.order_policy)}**.
        They accept the following payment methods: **${this.formatJSON(row.payment_methods)}**.
        Their operating hours are: **${this.formatJSON(row.shop_timings)}**.
        You can connect with them on social media via these links: **${this.formatJSON(row.shop_social_links)}**.
        Their subscription status is **${row.subscription_status ? "active" : "inactive"}**.
        Orders placed with them are **${row.shop_auto_accept ? "automatically accepted" : "not automatically accepted"}**.
        Customer support is **${row.enable_support ? "enabled" : "not enabled"}**.
        A pickup module is **${row.pickup_module ? "available" : "not available"}**.
        They **${row.generate_quotation ? "can generate quotations" : "do not generate quotations"}**.
        To order through WhatsApp, use this link: **https://wa.me/${row.phone_number}**.
        Regarding ordering directly from Cravin: **${row.is_only_for_listing ? "This shop is only for listing and direct orders are not supported through Cravin." : "You can order your favorite items directly through Cravin."}**
      `),
      metadata: {
        type: "shop",
        id: row.shop_id,
        name: row.shop_name,
        phone: row.phone_number,
        takes_orders: row.take_orders,
        subscription_status: row.subscription_status,
        pickup_available: row.pickup_module,
        order_link_through_whatsapp: "https://wa.me/" + row.phone_number,
        shop_under_cravin: !row.is_only_for_listing,
      },
    }));
  }

  async  vectorizeSportsLandingPages(client) {
    const query = `
      SELECT
        slp.*,
        c.club_name,
        c.is_only_for_listing
      FROM "SportsLandingPage" slp
      LEFT JOIN "Clubs" c ON slp.club_id = c.club_id
    `;

    const result = await client.query(query);

    return result.rows.map((row) => ({
      id: `sports_landing_${row.club_id}`,
      content: this.cleanText(`
        This landing page showcases the sports club **${row.club_name || "an unnamed club"}**.
        It is situated in the **${row.club_emirate || "an unspecified emirate"}**.
        The average spend at this venue is approximately **${row.average_spend || "not specified"}**.
        It's associated with tags such as: **${this.formatArray(row.club_tags)}**.
        Available amenities include: **${this.formatArray(row.amenities)}**.
        Here's some information about the venue: "${row.about_venue || 'No about venue information provided'}".
        You can contact them via landline at: **${this.formatArray(row.club_landline_numbers)}**.
        The club's location coordinates are **${
          row.latitude && row.longitude
            ? `${row.latitude}, ${row.longitude}`
            : "not available"
        }**.
        Find their social media presence through these links: **${this.formatJSON(row.social_links)}**.
        For detailed information about their facilities, see: **${this.formatJSON(row.facility_details)}**.
        Please note: **${row.is_only_for_listing ? "You cannot book directly from Cravin; this is for listing purposes only." : "You can book directly from Cravin."}**
      `),
      metadata: {
        type: "sports_landing",
        club_id: row.club_id,
        club_name: row.club_name,
        emirate: row.club_emirate,
        show_in_landing: row.show_in_landing_page,
        latitude: row.latitude,
        longitude: row.longitude,
        club_under_cravin: !row.is_only_for_listing,
      },
    }));
  }
  async vectorizeAllTables() {
    console.log("Starting vectorization process...");

    const vectorData = [];

    try {
      const tables = [
        { name: "AddOns", method: this.vectorizeAddOns },
        { name: "Branches", method: this.vectorizeBranches },
        { name: "Categories", method: this.vectorizeCategories },
        { name: "Clubs", method: this.vectorizeClubs },
        { name: "Courts", method: this.vectorizeCourts },
        { name: "DeliveryZones", method: this.vectorizeDeliveryZones },
        { name: "Discounts", method: this.vectorizeDiscounts },
        { name: "FAQCategory", method: this.vectorizeFAQCategories },
        { name: "FAQQuestions", method: this.vectorizeFAQQuestions },
        { name: "Facilities", method: this.vectorizeFacilities },
        { name: "FoodDiscounts", method: this.vectorizeFoodDiscounts },
        { name: "FoodItemTags", method: this.vectorizeFoodItemTags },
        { name: "FoodLandingPage", method: this.vectorizeFoodLandingPages },
        { name: "Items", method: this.vectorizeItems },
        { name: "Restaurants", method: this.vectorizeRestaurants },
        { name: "Shops", method: this.vectorizeShops },
        { name: "SportsLandingPage", method: this.vectorizeSportsLandingPages },
      ];

      for (const table of tables) {
        console.log(`Vectorizing ${table.name}...`);
        try {
          const data = await table.method.call(this);
          vectorData.push(...data);
          console.log(`${table.name}: ${data.length} records processed`);
        } catch (error) {
          console.error(`Error vectorizing ${table.name}:`, error.message);
        }
      }

      console.log(`\nTotal records vectorized: ${vectorData.length}`);
      // return vectorData;
      await this.saveToFile(vectorData);
    } catch (error) {
      console.error("Error in vectorization process:", error);
      throw error;
    }
  }

  async saveToFile(vectorData, filename = "vectorized_data.json") {
    const fs = require("fs");

    const output = {
      timestamp: new Date().toISOString(),
      total_records: vectorData.length,
      data: vectorData,
    };

    fs.writeFileSync(filename, JSON.stringify(output, null, 2));
    console.log(`Data saved to ${filename}`);
  }

  async getIncrementalUpdates(lastUpdateTime) {
    console.log(`Getting incremental updates since: ${lastUpdateTime}`);

    const vectorData = [];
    const modifiableQueries = [
      // {
      //   name: "AddOns",
      //   query: `SELECT * FROM "AddOns" WHERE modified_at > $1 OR created_at > $1`,
      //   method: this.vectorizeAddOnsIncremental,
      // },
      // Note: ShopAddOns incremental method not implemented yet
      // {
      //   name: "ShopAddOns",
      //   query: `SELECT * FROM "ShopAddOns" WHERE modified_at > $1 OR created_at > $1`,
      //   method: this.vectorizeShopAddOnsIncremental,
      // },
    ];

    const creatableQueries = [
      // Note: Incremental methods not implemented yet - commenting out to prevent errors
      // TODO: Implement these incremental methods for better performance
      // {
      //   name: "Branches",
      //   query: `SELECT * FROM "Branches" WHERE created_at > $1`,
      //   method: this.vectorizeBranchesIncremental,
      // },
      // {
      //   name: "Categories",
      //   query: `SELECT * FROM "Categories" WHERE created_at > $1`,
      //   method: this.vectorizeCategoriesIncremental,
      // },
      // {
      //   name: "Items",
      //   query: `SELECT * FROM "Items" WHERE created_at > $1`,
      //   method: this.vectorizeItemsIncremental,
      // },
      // {
      //   name: "Restaurants",
      //   query: `SELECT * FROM "Restaurants" WHERE created_at > $1`,
      //   method: this.vectorizeRestaurantsIncremental,
      // },
      // {
      //   name: "Shops",
      //   query: `SELECT * FROM "Shops" WHERE created_at > $1`,
      //   method: this.vectorizeShopsIncremental,
      // },
      // {
      //   name: "Clubs",
      //   query: `SELECT * FROM "Clubs" WHERE created_at > $1`,
      //   method: this.vectorizeClubsIncremental,
      // },
      // {
      //   name: "Facilities",
      //   query: `SELECT * FROM "Facilities" WHERE created_at > $1`,
      //   method: this.vectorizeFacilitiesIncremental,
      // },
      // {
      //   name: "FoodDiscounts",
      //   query: `SELECT * FROM "FoodDiscounts" WHERE created_at > $1`,
      //   method: this.vectorizeFoodDiscountsIncremental,
      // },
      // {
      //   name: "ShopDiscounts",
      //   query: `SELECT * FROM "ShopDiscounts" WHERE created_at > $1`,
      //   method: this.vectorizeShopDiscountsIncremental,
      // },
      // {
      //   name: "Discounts",
      //   query: `SELECT * FROM "Discounts" WHERE created_at > $1`,
      //   method: this.vectorizeDiscountsIncremental,
      // },
    ];

    const allQueries = [...modifiableQueries, ...creatableQueries];

    for (const queryConfig of allQueries) {
      try {
        const result = await this.client.query(queryConfig.query, [
          lastUpdateTime,
        ]);
        if (result.rows.length > 0) {
          const data = await queryConfig.method.call(this, result.rows);
          vectorData.push(...data);
          console.log(`${queryConfig.name}: ${data.length} updated records`);
        }
      } catch (error) {
        console.error(
          `Error getting incremental updates for ${queryConfig.name}:`,
          error.message
        );
      }
    }

    return vectorData;
  }

  async vectorizeAddOnsIncremental(rows) {
    return rows.map((row) => ({
      id: `addon_${row.add_on_id}`,
      content: this.cleanText(`
        Add-on: ${row.add_on_name}
        Type: ${row.add_on_type}
        Price: ${row.add_on_price}
        Branch ID: ${row.fk_branch_id || "N/A"}
      `),
      metadata: {
        type: "addon",
        id: row.add_on_id,
        name: row.add_on_name,
        addon_type: row.add_on_type,
        price: row.add_on_price,
        branch_id: row.fk_branch_id,
      },
    }));
  }

  async processInBatches(method, batchSize = 1000) {
    const allData = [];
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      try {
        const batchData = await method.call(this, offset, batchSize);
        allData.push(...batchData);

        if (batchData.length < batchSize) {
          hasMore = false;
        } else {
          offset += batchSize;
        }

        console.log(`Processed batch: ${offset} records`);
      } catch (error) {
        console.error(`Error processing batch at offset ${offset}:`, error);
        hasMore = false;
      }
    }

    return allData;
  }
}
 

async function main() {
  // Handle SSL certificate configuration for cloud databases
  let sslConfig = false;
  const fs = require("fs");

  const caCert = fs.readFileSync("cert-bundle.pem");
  
  // if (process.env.DB_SSL === 'true') {
    // For Neon and other cloud databases, use SSL with proper configuration
    sslConfig =   {
      ca: caCert
    }
    console.log("Using SSL configuration for cloud database");
  // } else {
  //   console.log("SSL disabled");
  // }

  const vectorizer = new VectorizationService({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_DATABASE,
    user:  process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    ssl: sslConfig,
  });

  try {
    await vectorizer.connect();
    console.log("Connected to database successfully");

    console.log("Starting vectorization process...");
    await vectorizer.vectorizeAllTables();

    // Uncomment below for incremental updates
    // const lastUpdate = '2025-01-01T00:00:00Z';
    // const incrementalData = await vectorizer.getIncrementalUpdates(lastUpdate);
    // console.log(`Incremental updates: ${incrementalData.length} records`);

    console.log("Vectorization completed successfully!");
  } catch (error) {
    console.error("Error during vectorization:", error);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  } finally {
    try {
      await vectorizer.disconnect();
      console.log("Database connection closed");
    } catch (disconnectError) {
      console.error("Error closing database connection:", disconnectError);
    }
  }
}

module.exports = { VectorizationService };

if (require.main === module) {
  main();
}
